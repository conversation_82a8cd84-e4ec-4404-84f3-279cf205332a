import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:movie_proj/core/shared/shared_pref.dart';
import 'package:movie_proj/feature/auth/manage/auth_cubit.dart';
import 'package:movie_proj/feature/home/<USER>';
import 'package:movie_proj/feature/suggest/suggest_screen.dart';
import 'package:movie_proj/feature/myList/my_list_screen.dart';
import 'package:movie_proj/feature/myFriends/my_friends_screen.dart';
import 'package:movie_proj/feature/profile/profile_screen.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    // Ensure user data is loaded when app starts
    _ensureUserDataLoaded();
  }

  void _ensureUserDataLoaded() async {
    final authCubit = context.read<AuthCubit>();

    // Check if user data is already loaded
    if (authCubit.userModel == null) {
      final uIdUser = CacheHelper.getString(key: 'uIdUser0');

      if (uIdUser != null && uIdUser.isNotEmpty) {
        await authCubit.getUserData(uIdUser);
      }
    }
  }

  void _onNavigate(int index) {
    // Special handling for profile screen navigation
    if (index == 4) {
      _navigateToProfile();
    } else {
      setState(() {
        _currentIndex = index;
      });
    }
  }

  void _navigateToProfile() async {
    final authCubit = context.read<AuthCubit>();

    // Ensure user data is loaded before navigating to profile
    if (authCubit.userModel == null) {
      final uIdUser = CacheHelper.getString(key: 'uIdUser0');

      if (uIdUser != null && uIdUser.isNotEmpty) {
        // Show loading indicator while loading user data
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 12),
                Text('Loading profile...'),
              ],
            ),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 2),
          ),
        );

        await authCubit.getUserData(uIdUser);
      }
    }

    // Navigate to profile screen
    setState(() {
      _currentIndex = 4;
    });
  }

  @override
  Widget build(BuildContext context) {
    return _buildCurrentScreen();
  }

  Widget _buildCurrentScreen() {
    switch (_currentIndex) {
      case 0:
        return HomeScreen(onNavigate: _onNavigate);
      case 1:
        return SuggestScreen(onNavigate: _onNavigate);
      case 2:
        return MyListScreen(onNavigate: _onNavigate);
      case 3:
        return MyFriendsScreen(onNavigate: _onNavigate);
      case 4:
        return _buildProfileScreen();
      default:
        return HomeScreen(onNavigate: _onNavigate);
    }
  }

  Widget _buildProfileScreen() {
    return BlocBuilder<AuthCubit, AuthStates>(
      builder: (context, state) {
        final authCubit = context.read<AuthCubit>();

        // Show loading screen if user data is being loaded
        if (state is GetUserLoadingState) {
          return Scaffold(
            backgroundColor: const Color(0xFF1A1A2E),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(
                    color: Colors.white,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Loading your profile...',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        // Show error screen if user data failed to load
        if (state is GetUserErrorState) {
          return Scaffold(
            backgroundColor: const Color(0xFF1A1A2E),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 64,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Failed to load profile',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    state.error,
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 30),
                  ElevatedButton(
                    onPressed: () {
                      _ensureUserDataLoaded();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Retry'),
                  ),
                  const SizedBox(height: 10),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _currentIndex = 0; // Go back to home
                      });
                    },
                    child: const Text(
                      'Go to Home',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        // Show profile screen if user data is loaded or loading completed
        return ProfileScreen(onNavigate: _onNavigate);
      },
    );
  }
}
