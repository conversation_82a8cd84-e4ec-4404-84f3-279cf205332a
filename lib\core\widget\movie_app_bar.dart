import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:movie_proj/core/my_colors.dart';
import 'package:movie_proj/core/my_styles.dart';
import 'package:movie_proj/core/my_text.dart';
import 'package:movie_proj/core/spacing.dart';
import 'package:movie_proj/feature/auth/manage/auth_cubit.dart';
import 'package:movie_proj/feature/auth/manage/auth_state.dart';
import 'package:movie_proj/feature/search/search_screen.dart';

class MovieAppBar extends StatelessWidget implements PreferredSizeWidget {
  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  final int currentIndex;
  final Function(int) onNavigate;

  const MovieAppBar({
    super.key,
    required this.currentIndex,
    required this.onNavigate,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: MyColors.primaryColor,
      title: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 30),
        child: Row(
          children: [
            BlocBuilder<AuthCubit, AuthStates>(
              builder: (context, state) {
                final authCubit = context.read<AuthCubit>();
                final user = authCubit.userModel;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      MyText.mood,
                      style: MyStyles.title13Redw700,
                    ),
                    Row(
                      children: [
                        Text(
                          MyText.box,
                          style:
                              MyStyles.title24White700.copyWith(fontSize: 20),
                        ),
                        if (user?.name != null && user!.name!.isNotEmpty) ...[
                          Text(
                            ' - ',
                            style:
                                MyStyles.title24White400.copyWith(fontSize: 16),
                          ),
                          Text(
                            'Hi, ${user.name!.split(' ').first}!',
                            style: MyStyles.title24White400.copyWith(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                );
              },
            ),
            hSpace(20),
            _buildNavigationItem(
              text: MyText.home,
              index: 0,
              isActive: currentIndex == 0,
            ),
            hSpace(20),
            _buildNavigationItem(
              text: MyText.suggest,
              index: 1,
              isActive: currentIndex == 1,
            ),
            hSpace(20),
            _buildNavigationItem(
              text: MyText.myList,
              index: 2,
              isActive: currentIndex == 2,
            ),
            hSpace(20),
            _buildNavigationItem(
              text: MyText.friends,
              index: 3,
              isActive: currentIndex == 3,
            ),
            const Spacer(),
            IconButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SearchScreen(),
                  ),
                );
              },
              icon: const Icon(
                Icons.search,
                color: Colors.white,
              ),
            ),
            hSpace(15),
            BlocBuilder<AuthCubit, AuthStates>(
              builder: (context, state) {
                final authCubit = context.read<AuthCubit>();
                final user = authCubit.userModel;

                return GestureDetector(
                  onTap: () => onNavigate(4), // Profile screen index
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: currentIndex == 4
                            ? MyColors.btnColor
                            : Colors.transparent,
                        width: 2,
                      ),
                    ),
                    child: _buildProfileAvatar(user),
                  ),
                );
              },
            )
          ],
        ),
      ),
    );
  }

  Widget _buildProfileAvatar(dynamic user) {
    final profileImage = _getProfileImage(user);

    if (profileImage != null) {
      return CircleAvatar(
        radius: 18,
        backgroundColor: Colors.grey[300],
        backgroundImage: profileImage,
        onBackgroundImageError: (exception, stackTrace) {
          debugPrint('Error loading profile image: $exception');
        },
      );
    } else {
      return CircleAvatar(
        radius: 18,
        backgroundColor: Colors.grey[300],
        child: const Icon(
          Icons.person,
          color: Colors.grey,
          size: 20,
        ),
      );
    }
  }

  ImageProvider? _getProfileImage(dynamic user) {
    if (user?.image != null && user!.image!.isNotEmpty) {
      try {
        return AssetImage(user.image!);
      } catch (e) {
        debugPrint('Error loading user profile image: $e');
        return null;
      }
    }
    return null;
  }

  Widget _buildNavigationItem({
    required String text,
    required int index,
    required bool isActive,
  }) {
    return GestureDetector(
      onTap: () => onNavigate(index),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: isActive
            ? BoxDecoration(
                color: MyColors.btnColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              )
            : null,
        child: Text(
          text,
          style: isActive
              ? MyStyles.title24White700.copyWith(fontSize: 13)
              : MyStyles.title24White400.copyWith(fontSize: 13),
        ),
      ),
    );
  }
}
