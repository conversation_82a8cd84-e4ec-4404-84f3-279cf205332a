import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:movie_proj/core/my_colors.dart';
import 'package:movie_proj/core/spacing.dart';
import 'package:movie_proj/feature/auth/manage/auth_cubit.dart';
import 'package:movie_proj/feature/auth/manage/auth_state.dart';

class ProfilePictureSelector extends StatelessWidget {
  const ProfilePictureSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthCubit, AuthStates>(
      builder: (context, state) {
        final authCubit = context.read<AuthCubit>();
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Choose Your Avatar',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            vSpace(12),
            
            // Gender Selection
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => authCubit.selectGender('boy'),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: authCubit.selectedGender == 'boy'
                            ? MyColors.btnColor
                            : Colors.transparent,
                        border: Border.all(
                          color: authCubit.selectedGender == 'boy'
                              ? MyColors.btnColor
                              : Colors.white.withValues(alpha: 0.3),
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.boy,
                            color: authCubit.selectedGender == 'boy'
                                ? Colors.white
                                : Colors.white.withValues(alpha: 0.7),
                            size: 20,
                          ),
                          hSpace(8),
                          Text(
                            'Boy',
                            style: TextStyle(
                              color: authCubit.selectedGender == 'boy'
                                  ? Colors.white
                                  : Colors.white.withValues(alpha: 0.7),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                hSpace(12),
                Expanded(
                  child: GestureDetector(
                    onTap: () => authCubit.selectGender('girl'),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: authCubit.selectedGender == 'girl'
                            ? MyColors.btnColor
                            : Colors.transparent,
                        border: Border.all(
                          color: authCubit.selectedGender == 'girl'
                              ? MyColors.btnColor
                              : Colors.white.withValues(alpha: 0.3),
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.girl,
                            color: authCubit.selectedGender == 'girl'
                                ? Colors.white
                                : Colors.white.withValues(alpha: 0.7),
                            size: 20,
                          ),
                          hSpace(8),
                          Text(
                            'Girl',
                            style: TextStyle(
                              color: authCubit.selectedGender == 'girl'
                                  ? Colors.white
                                  : Colors.white.withValues(alpha: 0.7),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            vSpace(16),
            
            // Avatar Selection
            Text(
              'Select Avatar',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            vSpace(8),
            
            Container(
              height: 100,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: authCubit.getCurrentGenderAvatars().length,
                itemBuilder: (context, index) {
                  final avatarPath = authCubit.getCurrentGenderAvatars()[index];
                  final isSelected = authCubit.selectedProfileImage == avatarPath;
                  
                  return GestureDetector(
                    onTap: () => authCubit.selectProfileImage(avatarPath),
                    child: Container(
                      margin: const EdgeInsets.only(right: 12),
                      child: Column(
                        children: [
                          Container(
                            width: 70,
                            height: 70,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: isSelected
                                    ? MyColors.btnColor
                                    : Colors.white.withValues(alpha: 0.3),
                                width: isSelected ? 3 : 1,
                              ),
                              image: DecorationImage(
                                image: AssetImage(avatarPath),
                                fit: BoxFit.cover,
                                onError: (exception, stackTrace) {
                                  debugPrint('Error loading avatar: $exception');
                                },
                              ),
                            ),
                            child: isSelected
                                ? Container(
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: MyColors.btnColor.withValues(alpha: 0.3),
                                    ),
                                    child: const Icon(
                                      Icons.check,
                                      color: Colors.white,
                                      size: 24,
                                    ),
                                  )
                                : null,
                          ),
                          vSpace(4),
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: isSelected
                                  ? MyColors.btnColor
                                  : Colors.transparent,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            
            // Selected Avatar Preview
            if (authCubit.selectedProfileImage != null) ...[
              vSpace(16),
              Center(
                child: Column(
                  children: [
                    Text(
                      'Selected Avatar',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontSize: 12,
                      ),
                    ),
                    vSpace(8),
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: MyColors.btnColor,
                          width: 2,
                        ),
                        image: DecorationImage(
                          image: AssetImage(authCubit.selectedProfileImage!),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        );
      },
    );
  }
}
