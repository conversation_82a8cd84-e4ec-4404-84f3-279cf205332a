import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:movie_proj/core/my_colors.dart';
import 'package:movie_proj/core/spacing.dart';
import 'package:movie_proj/core/widget/movie_app_bar.dart';
import 'package:movie_proj/feature/auth/manage/auth_cubit.dart';
import 'package:movie_proj/feature/auth/manage/auth_state.dart';
import 'package:movie_proj/feature/auth/login_screen.dart';
import 'package:movie_proj/feature/profile/widget/profile_header.dart';
import 'package:movie_proj/feature/profile/widget/profile_menu.dart';
import 'package:movie_proj/feature/profile/widget/profile_movies.dart';

class ProfileScreen extends StatelessWidget {
  final Function(int) onNavigate;

  const ProfileScreen({
    super.key,
    required this.onNavigate,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final horizontalPadding = _getHorizontalPadding(screenWidth);
    final verticalSpacing = _getVerticalSpacing(screenWidth);

    return Scaffold(
      backgroundColor: MyColors.primaryColor,
      appBar: MovieAppBar(
        currentIndex: 4,
        onNavigate: onNavigate,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: Column(
            children: [
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: _getMaxContainerWidth(screenWidth),
                ),
                child: Stack(
                  alignment: AlignmentDirectional.bottomCenter,
                  children: [
                    const ProfileHeader(),

                    //هخفيهه
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        margin: EdgeInsets.symmetric(
                          horizontal: _getMenuMargin(screenWidth),
                        ),
                        child: const ProfileMenu(),
                      ),
                    ),
                  ],
                ),
              ),
              vSpace(verticalSpacing),
              Container(
                constraints: const BoxConstraints(
                    // maxWidth: _getMaxContainerWidth(screenWidth),
                    ),
                child: Column(
                  children: [
                    vSpace(verticalSpacing / 2),
                    const ProfileMovies(),
                    vSpace(verticalSpacing),
                    _buildLogoutSection(context, screenWidth),
                  ],
                ),
              ),
              vSpace(verticalSpacing),
            ],
          ),
        ),
      ),
    );
  }

  double _getHorizontalPadding(double width) {
    if (width <= 400) return 12;
    if (width <= 600) return 20;
    if (width <= 900) return 30;
    return 40;
  }

  double _getVerticalSpacing(double width) {
    if (width <= 400) return 24;
    if (width <= 600) return 30;
    if (width <= 900) return 40;
    return 50;
  }

  double _getMaxContainerWidth(double width) {
    if (width <= 900) return width;
    return 1200;
  }

  double _getMenuMargin(double width) {
    if (width <= 400) return 8;
    if (width <= 600) return 16;
    if (width <= 900) return 24;
    return 32;
  }

  Widget _buildLogoutSection(BuildContext context, double screenWidth) {
    final horizontalPadding = _getHorizontalPadding(screenWidth);
    final buttonHeight = _getButtonHeight(screenWidth);
    final fontSize = _getLogoutFontSize(screenWidth);

    return BlocListener<AuthCubit, AuthStates>(
      listener: (context, state) {
        if (state is Unauthenticated) {
          // Navigate back to login screen
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(
              builder: (context) => const LoginScreen(),
            ),
            (route) => false, // Remove all previous routes
          );
        } else if (state is AuthErrorState) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Logout failed: ${state.error}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: horizontalPadding),
        child: ElevatedButton(
          onPressed: () {
            _showLogoutConfirmation(context);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red.withValues(alpha: 0.9),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            padding: EdgeInsets.symmetric(
              vertical: buttonHeight * 0.4,
              horizontal: 24,
            ),
            elevation: 4,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.logout,
                size: fontSize * 1.2,
              ),
              hSpace(12),
              Text(
                'Sign Out',
                style: TextStyle(
                  fontSize: fontSize,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showLogoutConfirmation(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: MyColors.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          contentPadding: const EdgeInsets.all(24),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.logout,
                  color: Colors.red,
                  size: 32,
                ),
              ),
              vSpace(20),
              const Text(
                'Sign Out',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
              vSpace(12),
              const Text(
                'Are you sure you want to sign out of your account?',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              vSpace(24),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(
                            color: Colors.white.withValues(alpha: 0.3)),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text(
                        'Cancel',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  hSpace(12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        // Show loading and perform logout
                        _performLogout(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text(
                        'Sign Out',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  void _performLogout(BuildContext context) {
    // Show loading snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            SizedBox(width: 12),
            Text('Signing out...'),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 2),
      ),
    );

    // Perform logout
    context.read<AuthCubit>().logout();
  }

  double _getButtonHeight(double width) {
    if (width <= 400) return 40;
    if (width <= 600) return 45;
    if (width <= 900) return 50;
    return 55;
  }

  double _getLogoutFontSize(double width) {
    if (width <= 400) return 14;
    if (width <= 600) return 16;
    if (width <= 900) return 18;
    return 20;
  }
}
